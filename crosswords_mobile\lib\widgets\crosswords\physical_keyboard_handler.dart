import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../providers/crossword_game_provider.dart';

class PhysicalKeyboardHandler extends StatefulWidget {
  final Widget child;

  const PhysicalKeyboardHandler({
    super.key,
    required this.child,
  });

  @override
  State<PhysicalKeyboardHandler> createState() =>
      _PhysicalKeyboardHandlerState();
}

class _PhysicalKeyboardHandlerState extends State<PhysicalKeyboardHandler> {
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Request focus when the widget is first built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    // Only handle key down events
    if (event is! KeyDownEvent) {
      return KeyEventResult.ignored;
    }

    final gameProvider = context.read<CrosswordGameProvider>();

    // Check if game is in playing state
    if (gameProvider.gameState != GameState.playing) {
      return KeyEventResult.ignored;
    }

    final key = event.logicalKey;

    // Handle letter input
    if (key.keyLabel.length == 1 &&
        RegExp(r'[a-zA-Z]').hasMatch(key.keyLabel)) {
      final letter = key.keyLabel.toUpperCase();
      gameProvider.inputCharacter(letter);
      return KeyEventResult.handled;
    }

    // Handle special keys
    switch (key) {
      case LogicalKeyboardKey.backspace:
        gameProvider.deleteCharacter();
        return KeyEventResult.handled;

      case LogicalKeyboardKey.delete:
        gameProvider.clearCurrentCell();
        return KeyEventResult.handled;

      case LogicalKeyboardKey.space:
        gameProvider.toggleDirection();
        return KeyEventResult.handled;

      case LogicalKeyboardKey.enter:
        gameProvider.toggleDirection();
        return KeyEventResult.handled;

      case LogicalKeyboardKey.tab:
        gameProvider.toggleDirection();
        return KeyEventResult.handled;

      case LogicalKeyboardKey.arrowUp:
        gameProvider.moveUp();
        return KeyEventResult.handled;

      case LogicalKeyboardKey.arrowDown:
        gameProvider.moveDown();
        return KeyEventResult.handled;

      case LogicalKeyboardKey.arrowLeft:
        gameProvider.moveLeft();
        return KeyEventResult.handled;

      case LogicalKeyboardKey.arrowRight:
        gameProvider.moveRight();
        return KeyEventResult.handled;

      case LogicalKeyboardKey.escape:
        // Pause game or clear selection
        if (gameProvider.gameState == GameState.playing) {
          gameProvider.pauseGame();
        }
        return KeyEventResult.handled;

      default:
        return KeyEventResult.ignored;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      onKeyEvent: _handleKeyEvent,
      child: GestureDetector(
        onTap: () {
          // Ensure focus is maintained when tapping
          _focusNode.requestFocus();
        },
        behavior: HitTestBehavior.translucent,
        child: widget.child,
      ),
    );
  }
}
