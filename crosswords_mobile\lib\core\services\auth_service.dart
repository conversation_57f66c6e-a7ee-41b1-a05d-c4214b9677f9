import 'dart:convert';
import 'package:jwt_decoder/jwt_decoder.dart';

import '../models/crossword_models.dart';
import 'api_service.dart';
import 'storage_service.dart';

class AuthService {
  final ApiService _apiService;
  final StorageService _storageService;

  AuthService(this._apiService, this._storageService);

  // Check if user is authenticated
  Future<bool> isAuthenticated() async {
    final token = await _storageService.getToken();
    if (token == null) return false;

    try {
      // Check if token is expired
      return !JwtDecoder.isExpired(token);
    } catch (e) {
      // If token is invalid, clear it
      await _storageService.clearToken();
      return false;
    }
  }

  // Get current user from storage
  Future<User?> getCurrentUser() async {
    if (await isAuthenticated()) {
      return await _storageService.getUser();
    }
    return null;
  }

  // Login with email and password
  Future<AuthResult> login(String email, String password) async {
    try {
      final response = await _apiService.login(email, password);

      // Debug logging for login response
      print('🔍 Login response status: ${response.status}');
      print('🔍 Login response message: ${response.message}');
      print('🔍 Login response data: ${response.data}');

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final token = data['token'] as String?;
        final userData = data['user'] as Map<String, dynamic>?;

        print('🔍 Extracted token: ${token != null ? token : 'Missing'}');
        print(
            '🔍 Extracted user data: ${userData != null ? 'Present' : 'Missing'}');

        if (token != null && userData != null) {
          // Store token and user data
          await _storageService.setToken(token);
          final user = User.fromJson(userData);
          await _storageService.setUser(user);

          print('✅ Login successful - user stored');
          return AuthResult.success(user);
        } else {
          print('❌ Login failed - missing token or user data');
          return AuthResult.failure('Data login tidak lengkap');
        }
      }

      print('❌ Login failed - response not successful or no data');
      return AuthResult.failure(
          response.message.isNotEmpty ? response.message : 'Login gagal');
    } on ApiException catch (e) {
      print('❌ Login failed - API Exception: ${e.message}');
      return AuthResult.failure(e.message);
    } catch (e) {
      print('❌ Login failed - General Exception: $e');
      return AuthResult.failure('Terjadi kesalahan saat login');
    }
  }

  // Register new user
  Future<AuthResult> register(
    String username,
    String email,
    String password,
    String? displayName,
  ) async {
    try {
      final response = await _apiService.register(
        username,
        email,
        password,
        displayName,
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final token = data['token'] as String?;
        final userData = data['user'] as Map<String, dynamic>?;

        if (token != null && userData != null) {
          // Store token and user data
          await _storageService.setToken(token);
          final user = User.fromJson(userData);
          await _storageService.setUser(user);

          return AuthResult.success(user);
        }
      }

      return AuthResult.failure(response.message);
    } on ApiException catch (e) {
      return AuthResult.failure(e.message);
    } catch (e) {
      return AuthResult.failure('Terjadi kesalahan saat mendaftar');
    }
  }

  // Refresh user data from server
  Future<AuthResult> refreshUser() async {
    try {
      if (!await isAuthenticated()) {
        return AuthResult.failure('Tidak terautentikasi');
      }

      final response = await _apiService.getCurrentUser();

      if (response.isSuccess && response.data != null) {
        final user = response.data!;
        await _storageService.setUser(user);
        return AuthResult.success(user);
      }

      return AuthResult.failure(response.message);
    } on ApiException catch (e) {
      if (e.message.contains('Sesi telah berakhir')) {
        await logout();
      }
      return AuthResult.failure(e.message);
    } catch (e) {
      return AuthResult.failure(
          'Terjadi kesalahan saat memperbarui data pengguna');
    }
  }

  // Update user profile
  Future<AuthResult> updateProfile(Map<String, dynamic> data) async {
    try {
      if (!await isAuthenticated()) {
        return AuthResult.failure('Tidak terautentikasi');
      }

      final response = await _apiService.updateProfile(data);

      if (response.isSuccess && response.data != null) {
        final user = response.data!;
        await _storageService.setUser(user);
        return AuthResult.success(user);
      }

      return AuthResult.failure(response.message);
    } on ApiException catch (e) {
      return AuthResult.failure(e.message);
    } catch (e) {
      return AuthResult.failure('Terjadi kesalahan saat memperbarui profil');
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      // Try to call logout endpoint
      if (await isAuthenticated()) {
        await _apiService.logout();
      }
    } catch (e) {
      // Ignore errors during logout API call
    } finally {
      // Always clear local data
      await _storageService.clearToken();
      await _storageService.clearUser();
      await _storageService.clearAllProgress();
    }
  }

  // Validate token and get user info
  Future<Map<String, dynamic>?> getTokenInfo() async {
    final token = await _storageService.getToken();
    if (token == null) return null;

    try {
      final decodedToken = JwtDecoder.decode(token);
      return decodedToken;
    } catch (e) {
      await _storageService.clearToken();
      return null;
    }
  }

  // Check if token will expire soon (within 1 hour)
  Future<bool> isTokenExpiringSoon() async {
    final token = await _storageService.getToken();
    if (token == null) return true;

    try {
      final expirationDate = JwtDecoder.getExpirationDate(token);
      final now = DateTime.now();
      final difference = expirationDate.difference(now);

      return difference.inHours < 1;
    } catch (e) {
      return true;
    }
  }

  // Auto-refresh token if needed
  Future<bool> autoRefreshIfNeeded() async {
    if (await isTokenExpiringSoon()) {
      final result = await refreshUser();
      return result.isSuccess;
    }
    return true;
  }
}

// Auth result class
class AuthResult {
  final bool isSuccess;
  final String message;
  final User? user;

  AuthResult._({
    required this.isSuccess,
    required this.message,
    this.user,
  });

  factory AuthResult.success(User user) {
    return AuthResult._(
      isSuccess: true,
      message: 'Berhasil',
      user: user,
    );
  }

  factory AuthResult.failure(String message) {
    return AuthResult._(
      isSuccess: false,
      message: message,
    );
  }
}
