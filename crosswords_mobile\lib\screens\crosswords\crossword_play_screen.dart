import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/models/crossword_models.dart';
import '../../providers/crossword_provider.dart';
import '../../providers/crossword_game_provider.dart';

import '../../widgets/crosswords/crossword_clues.dart' as clues_widget;
import '../../widgets/crosswords/mobile_keyboard.dart';
import '../../widgets/crosswords/hint_dialog.dart';
import '../../widgets/crosswords/physical_keyboard_handler.dart'
    as keyboard_handler;
import '../../widgets/crosswords/responsive_crossword_grid.dart'
    as responsive_grid;

/// Optimized game state class for Selector widget
class _GameState {
  final GameState gameState;
  final SelectedCell? selectedCell;
  final double progressPercentage;
  final int completedCells;
  final int totalCells;
  final Duration timeSpent;

  const _GameState({
    required this.gameState,
    required this.selectedCell,
    required this.progressPercentage,
    required this.completedCells,
    required this.totalCells,
    required this.timeSpent,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is _GameState &&
        other.gameState == gameState &&
        other.selectedCell == selectedCell &&
        other.progressPercentage == progressPercentage &&
        other.completedCells == completedCells &&
        other.totalCells == totalCells &&
        other.timeSpent == timeSpent;
  }

  @override
  int get hashCode {
    return Object.hash(
      gameState,
      selectedCell,
      progressPercentage,
      completedCells,
      totalCells,
      timeSpent,
    );
  }
}

/// Enhanced crossword play screen with optimized performance and UX
class CrosswordPlayScreen extends StatefulWidget {
  final String crosswordId;
  final bool resumeProgress;

  const CrosswordPlayScreen({
    super.key,
    required this.crosswordId,
    this.resumeProgress = false,
  });

  @override
  State<CrosswordPlayScreen> createState() => _CrosswordPlayScreenState();
}

class _CrosswordPlayScreenState extends State<CrosswordPlayScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  // State variables
  bool _isLoading = true;
  bool _showKeyboard = false;
  Crossword? _crossword;
  String? _error;
  bool _isRetrying = false;

  // Animation controllers for smooth transitions
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Zoom control
  final TransformationController _transformationController =
      TransformationController();

  // Gesture detection for double-tap zoom
  late AnimationController _doubleTapAnimationController;
  Animation<Matrix4>? _doubleTapAnimation;

  // Timer for continuous time updates
  Timer? _gameTimer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeAnimations();
    _loadCrossword();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _gameTimer?.cancel();
    _fadeController.dispose();
    _slideController.dispose();
    _doubleTapAnimationController.dispose();
    _transformationController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Auto-pause game when app goes to background
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.inactive) {
      final gameProvider = context.read<CrosswordGameProvider>();
      if (gameProvider.gameState == GameState.playing) {
        gameProvider.pauseGame();
        _stopTimer();
      }
    }
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _doubleTapAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
  }

  Future<void> _loadCrossword() async {
    if (!mounted) return;

    try {
      setState(() {
        _isLoading = true;
        _error = null;
        _isRetrying = false;
      });

      final crosswordProvider = context.read<CrosswordProvider>();
      final crossword =
          await crosswordProvider.getCrossword(widget.crosswordId);

      if (!mounted) return;

      if (crossword != null) {
        setState(() {
          _crossword = crossword;
          _isLoading = false;
        });

        // Initialize game with error handling
        try {
          final gameProvider = context.read<CrosswordGameProvider>();

          // Stop any existing timer before initializing new game
          _stopTimer();

          // Reset zoom transformation for new puzzle
          _resetZoom();

          await gameProvider.initializeGame(
            crossword,
            resumeProgress: widget.resumeProgress,
          );

          // Start animations after successful load
          _fadeController.forward();
          _slideController.forward();
        } catch (gameError) {
          if (mounted) {
            setState(() {
              _error =
                  'Gagal menginisialisasi permainan: ${gameError.toString()}';
              _isLoading = false;
            });
          }
        }
      } else {
        setState(() {
          _error = 'Teka-teki silang tidak ditemukan';
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error =
              'Terjadi kesalahan saat memuat teka-teki silang: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _retryLoad() async {
    setState(() {
      _isRetrying = true;
    });

    await Future.delayed(const Duration(milliseconds: 500));
    await _loadCrossword();

    if (mounted) {
      setState(() {
        _isRetrying = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingScreen();
    }

    if (_error != null) {
      return _buildErrorScreen();
    }

    return keyboard_handler.PhysicalKeyboardHandler(
      child: Scaffold(
        body: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: OrientationBuilder(
                builder: (context, orientation) {
                  return orientation == Orientation.portrait
                      ? _buildPortraitLayout()
                      : _buildLandscapeLayout();
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingScreen() {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Memuat...'),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 60,
              height: 60,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Memuat teka-teki silang...',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Mohon tunggu sebentar',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScreen() {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Error'),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.error_outline,
                  size: 48,
                  color: theme.colorScheme.onErrorContainer,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'Oops! Terjadi Kesalahan',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                _error!,
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Kembali'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton.icon(
                    onPressed: _isRetrying ? null : _retryLoad,
                    icon: _isRetrying
                        ? SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                theme.colorScheme.onPrimary,
                              ),
                            ),
                          )
                        : const Icon(Icons.refresh),
                    label: Text(_isRetrying ? 'Mencoba...' : 'Coba Lagi'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPortraitLayout() {
    return Selector<CrosswordGameProvider, _GameState>(
      selector: (context, provider) => _GameState(
        gameState: provider.gameState,
        selectedCell: provider.selectedCell,
        progressPercentage: provider.progressPercentage,
        completedCells: provider.completedCells,
        totalCells: provider.totalCells,
        timeSpent: provider.timeSpent,
      ),
      builder: (context, gameState, child) {
        // Get keyboard height to adjust layout
        final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
        final isKeyboardVisible = keyboardHeight > 0 || _showKeyboard;

        return Column(
          children: [
            // App Bar - cached to prevent rebuilds
            child!,

            // Game Info
            Consumer<CrosswordGameProvider>(
              builder: (context, gameProvider, _) =>
                  _buildGameInfo(gameProvider),
            ),

            // Main content area with keyboard-aware scrolling
            Expanded(
              child: Stack(
                children: [
                  // Scrollable content area
                  SingleChildScrollView(
                    physics: const ClampingScrollPhysics(),
                    padding: EdgeInsets.only(
                      bottom: isKeyboardVisible
                          ? 320
                          : 80, // More space for keyboard and clues
                    ),
                    child: Column(
                      children: [
                        // Crossword Grid - responsive and scrollable when zoomed
                        SizedBox(
                          height: MediaQuery.of(context).size.height * 0.45,
                          width: double.infinity,
                          child: RepaintBoundary(
                            child: _buildZoomableGrid(),
                          ),
                        ),

                        // Selected Clue with animation - reduced size
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          child: clues_widget.CrosswordClues(
                            crossword: _crossword!,
                            showOnlySelected: true,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Zoom controls overlay
                  _buildZoomControls(),
                ],
              ),
            ),

            // Virtual Keyboard with smooth animation
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (child, animation) {
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0, 1),
                    end: Offset.zero,
                  ).animate(CurvedAnimation(
                    parent: animation,
                    curve: Curves.easeInOut,
                  )),
                  child: child,
                );
              },
              child: _showKeyboard
                  ? MobileKeyboard(
                      key: const ValueKey('keyboard'),
                      onHide: () => _toggleKeyboard(false),
                    )
                  : const SizedBox.shrink(key: ValueKey('no-keyboard')),
            ),
          ],
        );
      },
      child: Consumer<CrosswordGameProvider>(
        builder: (context, gameProvider, _) => _buildAppBar(gameProvider),
      ),
    );
  }

  Widget _buildLandscapeLayout() {
    return Selector<CrosswordGameProvider, _GameState>(
      selector: (context, provider) => _GameState(
        gameState: provider.gameState,
        selectedCell: provider.selectedCell,
        progressPercentage: provider.progressPercentage,
        completedCells: provider.completedCells,
        totalCells: provider.totalCells,
        timeSpent: provider.timeSpent,
      ),
      builder: (context, gameState, child) {
        return Column(
          children: [
            // App Bar - cached
            child!,

            // Main Content
            Expanded(
              child: Row(
                children: [
                  // Crossword Grid Section
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        // Game Info
                        Consumer<CrosswordGameProvider>(
                          builder: (context, gameProvider, _) =>
                              _buildGameInfo(gameProvider),
                        ),

                        // Grid with RepaintBoundary for performance
                        Expanded(
                          child: Stack(
                            children: [
                              RepaintBoundary(
                                child: _buildZoomableGrid(),
                              ),
                              // Zoom controls for landscape
                              _buildZoomControls(),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Clues Panel with improved styling
                  Expanded(
                    flex: 1,
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border(
                          left: BorderSide(
                            color: Theme.of(context)
                                .colorScheme
                                .outline
                                .withValues(alpha: 0.2),
                            width: 1,
                          ),
                        ),
                        color: Theme.of(context)
                            .colorScheme
                            .surface
                            .withValues(alpha: 0.5),
                      ),
                      child: RepaintBoundary(
                        child: clues_widget.CrosswordClues(
                          crossword: _crossword!,
                          showOnlySelected: false,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Virtual Keyboard with animation
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (child, animation) {
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0, 1),
                    end: Offset.zero,
                  ).animate(CurvedAnimation(
                    parent: animation,
                    curve: Curves.easeInOut,
                  )),
                  child: child,
                );
              },
              child: _showKeyboard
                  ? MobileKeyboard(
                      key: const ValueKey('keyboard'),
                      onHide: () => _toggleKeyboard(false),
                    )
                  : const SizedBox.shrink(key: ValueKey('no-keyboard')),
            ),
          ],
        );
      },
      child: Consumer<CrosswordGameProvider>(
        builder: (context, gameProvider, _) => _buildAppBar(gameProvider),
      ),
    );
  }

  void _toggleKeyboard(bool show) {
    if (mounted) {
      setState(() {
        _showKeyboard = show;
      });
    }
  }

  Widget _buildAppBar(CrosswordGameProvider gameProvider) {
    final theme = Theme.of(context);

    return SafeArea(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          border: Border(
            bottom: BorderSide(
              color: theme.colorScheme.outline.withValues(alpha: 0.3),
            ),
          ),
        ),
        child: Row(
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => _onBackPressed(gameProvider),
            ),

            Expanded(
              child: Text(
                _crossword?.title ?? 'Teka-Teki Silang',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // Game controls
            IconButton(
              icon: Icon(
                gameProvider.gameState == GameState.playing
                    ? Icons.pause
                    : Icons.play_arrow,
              ),
              onPressed: () => _toggleGameState(gameProvider),
            ),

            IconButton(
              icon: const Icon(Icons.lightbulb_outline),
              onPressed: gameProvider.gameState == GameState.playing
                  ? () => _showHintDialog(gameProvider)
                  : null,
              tooltip: 'Petunjuk',
            ),

            IconButton(
              icon: const Icon(Icons.keyboard),
              onPressed: () => setState(() => _showKeyboard = !_showKeyboard),
            ),

            PopupMenuButton<String>(
              onSelected: (value) => _onMenuSelected(value, gameProvider),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'restart',
                  child: Row(
                    children: [
                      Icon(Icons.restart_alt),
                      SizedBox(width: 8),
                      Text('Mulai Ulang'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'settings',
                  child: Row(
                    children: [
                      Icon(Icons.settings),
                      SizedBox(width: 8),
                      Text('Pengaturan'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGameInfo(CrosswordGameProvider gameProvider) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // Progress
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Progress',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: gameProvider.progressPercentage / 100,
                  backgroundColor: theme.colorScheme.surfaceContainerHighest,
                ),
                const SizedBox(height: 2),
                Text(
                  '${gameProvider.completedCells}/${gameProvider.totalCells} (${gameProvider.progressPercentage.toInt()}%)',
                  style: theme.textTheme.bodySmall,
                ),
              ],
            ),
          ),

          const SizedBox(width: 16),

          // Time
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                'Waktu',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _formatDuration(gameProvider.timeSpent),
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  void _toggleGameState(CrosswordGameProvider gameProvider) {
    if (gameProvider.gameState == GameState.playing) {
      gameProvider.pauseGame();
      _stopTimer();
    } else if (gameProvider.gameState == GameState.paused ||
        gameProvider.gameState == GameState.idle) {
      gameProvider.startGame();
      _startTimer();
    }
  }

  void _onBackPressed(CrosswordGameProvider gameProvider) {
    if (gameProvider.gameState == GameState.playing) {
      gameProvider.pauseGame();
      _stopTimer();
    }

    // Show confirmation dialog if game is in progress
    if (gameProvider.completedCells > 0) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Keluar dari Permainan'),
          content:
              const Text('Progress Anda akan disimpan. Yakin ingin keluar?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Batal'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop();
              },
              child: const Text('Keluar'),
            ),
          ],
        ),
      );
    } else {
      Navigator.of(context).pop();
    }
  }

  void _onMenuSelected(String value, CrosswordGameProvider gameProvider) {
    switch (value) {
      case 'restart':
        _showRestartDialog(gameProvider);
        break;
      case 'settings':
        _showSettingsDialog(gameProvider);
        break;
    }
  }

  void _showRestartDialog(CrosswordGameProvider gameProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Mulai Ulang'),
        content: const Text(
            'Yakin ingin memulai ulang? Progress saat ini akan hilang.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Batal'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Reset timer and zoom when restarting
              _stopTimer();
              _resetZoom();
              gameProvider.initializeGame(_crossword!);
            },
            child: const Text('Mulai Ulang'),
          ),
        ],
      ),
    );
  }

  void _showHintDialog(CrosswordGameProvider gameProvider) {
    showDialog(
      context: context,
      builder: (context) => const HintDialog(),
    );
  }

  void _showSettingsDialog(CrosswordGameProvider gameProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Pengaturan Permainan'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('Auto Move'),
              subtitle: const Text('Pindah otomatis ke kotak berikutnya'),
              value: gameProvider.autoMoveEnabled,
              onChanged: gameProvider.setAutoMove,
            ),
            SwitchListTile(
              title: const Text('Vibration'),
              subtitle: const Text('Getaran saat mengetik'),
              value: gameProvider.vibrationEnabled,
              onChanged: gameProvider.setVibration,
            ),
            SwitchListTile(
              title: const Text('Show Errors'),
              subtitle: const Text('Tampilkan jawaban yang salah'),
              value: gameProvider.showErrors,
              onChanged: gameProvider.setShowErrors,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Tutup'),
          ),
        ],
      ),
    );
  }

  Widget _buildZoomableGrid() {
    return InteractiveViewer(
      transformationController: _transformationController,
      boundaryMargin: const EdgeInsets.all(20),
      minScale: 0.5,
      maxScale: 3.0,
      panEnabled: true,
      scaleEnabled: true, // This enables pinch-to-zoom
      constrained: false,
      clipBehavior: Clip.none,
      onInteractionStart: (details) {
        // Debug: Log when interaction starts
        debugPrint(
            'InteractiveViewer interaction started: ${details.pointerCount} pointers');
      },
      onInteractionUpdate: (details) {
        // Debug: Log scale changes during interaction
        final scale = _transformationController.value.getMaxScaleOnAxis();
        debugPrint('InteractiveViewer scale: $scale');
      },
      onInteractionEnd: (details) {
        // Debug: Log when interaction ends
        debugPrint('InteractiveViewer interaction ended');
      },
      child: GestureDetector(
        onDoubleTap: _handleDoubleTap,
        behavior: HitTestBehavior.translucent,
        child: SizedBox(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height * 0.45,
          child: responsive_grid.ResponsiveCrosswordGrid(
            crossword: _crossword!,
            enableZoom:
                false, // Disable internal zoom since we're handling it here
          ),
        ),
      ),
    );
  }

  Widget _buildZoomControls() {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0 || _showKeyboard;

    return Positioned(
      right: 16,
      bottom: isKeyboardVisible
          ? 340
          : 120, // Dynamic positioning based on keyboard
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton.small(
            heroTag: "zoom_in",
            onPressed: _zoomIn,
            backgroundColor:
                Theme.of(context).colorScheme.surface.withValues(alpha: 0.9),
            elevation: 4,
            child: Icon(
              Icons.zoom_in,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          FloatingActionButton.small(
            heroTag: "zoom_out",
            onPressed: _zoomOut,
            backgroundColor:
                Theme.of(context).colorScheme.surface.withValues(alpha: 0.9),
            elevation: 4,
            child: Icon(
              Icons.zoom_out,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          FloatingActionButton.small(
            heroTag: "zoom_reset",
            onPressed: _resetZoom,
            backgroundColor:
                Theme.of(context).colorScheme.surface.withValues(alpha: 0.9),
            elevation: 4,
            child: Icon(
              Icons.center_focus_strong,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  void _zoomIn() {
    final currentScale = _transformationController.value.getMaxScaleOnAxis();
    final newScale = (currentScale * 1.5).clamp(0.5, 3.0);
    _animateToScale(newScale);
  }

  void _zoomOut() {
    final currentScale = _transformationController.value.getMaxScaleOnAxis();
    final newScale = (currentScale / 1.5).clamp(0.5, 3.0);
    _animateToScale(newScale);
  }

  void _resetZoom() {
    _animateToScale(1.0);
  }

  void _handleDoubleTap() {
    final currentScale = _transformationController.value.getMaxScaleOnAxis();
    final targetScale = currentScale > 1.5 ? 1.0 : 2.0;
    _animateToScale(targetScale);
  }

  void _animateToScale(double scale) {
    final currentTransform = _transformationController.value;
    final currentScale = currentTransform.getMaxScaleOnAxis();

    if ((scale - currentScale).abs() < 0.01) return; // Already at target scale

    // Calculate the center point for scaling
    final Offset center = Offset(
      MediaQuery.of(context).size.width / 2,
      MediaQuery.of(context).size.height / 2,
    );

    // Create target transformation with proper centering
    final targetTransform = Matrix4.identity()
      ..translate(center.dx, center.dy)
      ..scale(scale)
      ..translate(-center.dx, -center.dy);

    // Create animation
    _doubleTapAnimation = Matrix4Tween(
      begin: currentTransform,
      end: targetTransform,
    ).animate(CurvedAnimation(
      parent: _doubleTapAnimationController,
      curve: Curves.easeInOut,
    ));

    _doubleTapAnimationController.reset();
    _doubleTapAnimation!.addListener(() {
      _transformationController.value = _doubleTapAnimation!.value;
    });

    _doubleTapAnimationController.forward();
  }

  // Timer management methods
  void _startTimer() {
    _stopTimer(); // Stop any existing timer
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        final gameProvider = context.read<CrosswordGameProvider>();
        if (gameProvider.gameState == GameState.playing) {
          // Trigger a rebuild to update the timer display
          setState(() {});
        } else {
          // Stop timer if game is not playing
          _stopTimer();
        }
      }
    });
  }

  void _stopTimer() {
    _gameTimer?.cancel();
    _gameTimer = null;
  }
}
